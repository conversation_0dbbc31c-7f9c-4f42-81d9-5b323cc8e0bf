<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WEATHER APP</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>WEATHER APP</h1>
        <div class="input-container">
            <input type="text" id="city-input" placeholder="Enter city name">
            <button id="get-weather-btn">GET WEATHER INFO</button>
        </div>
        <div id="weather-info" class="hidden">
            <h2 id="city-name"></h2>
            <h2 id="temperature"></h2>
            <h2 id="description"></h2>
        </div>
        <p id="error-message" class="hidden"> City not found. Pleaese try again later</p>
    </div>
    <script src="script.js"></script>
</body>
</html>