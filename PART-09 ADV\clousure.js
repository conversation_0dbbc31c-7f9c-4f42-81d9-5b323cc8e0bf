// CLOSURE EXPLANATION AND WORKFLOW

function outer() {
    let counter = 4;  // ← This variable is in the outer function's scope
    
    // Returning an inner function (this creates a closure)
    return function() {
        counter++;    // ← Inner function can access outer function's 'counter'
        return counter;
    };
}

// Step 1: Call outer() - this creates the closure
let increment = outer();
// At this point:
// - outer() has finished executing
// - BUT the inner function still remembers 'counter = 4'
// - increment now holds the inner function with access to 'counter'

console.log("First call:", increment());  // counter becomes 5, returns 5
console.log("Second call:", increment()); // counter becomes 6, returns 6  
console.log("Third call:", increment());  // counter becomes 7, returns 7

// DEMONSTRATING MULTIPLE CLOSURES
console.log("\n--- Multiple Independent Closures ---");

// Each call to outer() creates a NEW closure with its OWN counter
let increment1 = outer();  // Creates closure with counter = 4
let increment2 = outer();  // Creates ANOTHER closure with counter = 4

console.log("increment1 first call:", increment1());  // 5
console.log("increment2 first call:", increment2());  // 5
console.log("increment1 second call:", increment1()); // 6
console.log("increment2 second call:", increment2()); // 6

// Each closure maintains its own separate state!

// PRACTICAL EXAMPLE: Creating a Counter Factory
console.log("\n--- Counter Factory Example ---");

function createCounter(initialValue) {
    let count = initialValue;
    
    return {
        increment: function() {
            count++;
            return count;
        },
        decrement: function() {
            count--;
            return count;
        },
        getValue: function() {
            return count;
        },
        reset: function() {
            count = initialValue;
            return count;
        }
    };
}

const counter1 = createCounter(10);
const counter2 = createCounter(100);

console.log("Counter1 increment:", counter1.increment()); // 11
console.log("Counter1 increment:", counter1.increment()); // 12
console.log("Counter2 increment:", counter2.increment()); // 101
console.log("Counter1 value:", counter1.getValue());     // 12
console.log("Counter2 value:", counter2.getValue());     // 101

// DEMONSTRATING DATA PRIVACY
console.log("\n--- Data Privacy Example ---");

function createBankAccount(initialBalance) {
    let balance = initialBalance;  // Private variable - cannot be accessed directly
    
    return {
        deposit: function(amount) {
            if (amount > 0) {
                balance += amount;
                return `Deposited $${amount}. New balance: $${balance}`;
            }
            return "Invalid deposit amount";
        },
        withdraw: function(amount) {
            if (amount > 0 && amount <= balance) {
                balance -= amount;
                return `Withdrew $${amount}. New balance: $${balance}`;
            }
            return "Invalid withdrawal amount or insufficient funds";
        },
        getBalance: function() {
            return `Current balance: $${balance}`;
        }
    };
}

const myAccount = createBankAccount(1000);
console.log(myAccount.getBalance());           // Current balance: $1000
console.log(myAccount.deposit(500));           // Deposited $500. New balance: $1500
console.log(myAccount.withdraw(200));          // Withdrew $200. New balance: $1300

// Try to access balance directly - THIS WON'T WORK!
console.log("Direct balance access:", myAccount.balance); // undefined
// The 'balance' variable is truly private thanks to closure!