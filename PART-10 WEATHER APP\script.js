document.addEventListener("DOMContentLoaded", function () {
    const cityInput = document.getElementById("city-input");
    const getWeatherBtn = document.getElementById("get-weather-btn");
    const weatherInfo = document.getElementById("weather-info");
    const cityNameDisplay = document.getElementById("city-name");
    const temperatureDisplay = document.getElementById("temperature");
    const descriptionDisplay = document.getElementById("description");
    const errorMessage = document.getElementById("error-message");

    const API_KEY = "c8013c6723403f79f64f87a9b45779da";

    getWeatherBtn.addEventListener("click", async function () {
        const city = cityInput.value.trim();
        if (!city) {
            showError("Please enter a city name");
            return;
        }

        // Show loading state
        showLoading();

        try {
            const weatherData = await fetchWeatherData(city);
            displayWeatherData(weatherData);
            hideError();
        } catch (error) {
            console.error("Weather fetch error:", error);
            showError("City not found. Please try again.");
        } finally {
            hideLoading();
        }
    });

    // Allow Enter key to search
    cityInput.addEventListener("keypress", function (event) {
        if (event.key === "Enter") {
            getWeatherBtn.click();
        }
    });

    async function fetchWeatherData(city) {
        // Correct API URL for current weather
        const url = `https://api.openweathermap.org/data/2.5/weather?q=${city}&appid=${API_KEY}&units=metric`;

        const response = await fetch(url);
        console.log("Response status:", response.status);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log("Weather data:", data);
        return data;
    }

    function displayWeatherData(weatherData) {
        // Hide error message if showing
        errorMessage.classList.add("hidden");
        
        // Display weather information
        cityNameDisplay.textContent = `${weatherData.name}, ${weatherData.sys.country}`;
        temperatureDisplay.textContent = `${Math.round(weatherData.main.temp)}°C`;
        descriptionDisplay.textContent = weatherData.weather[0].description;
        
        // Show weather info
        weatherInfo.classList.remove("hidden");
        
        // Clear input
        cityInput.value = "";
    }

    function showError(message = "An error occurred") {
        weatherInfo.classList.add("hidden");
        errorMessage.textContent = message;
        errorMessage.classList.remove("hidden");
    }

    function hideError() {
        errorMessage.classList.add("hidden");
    }

    function showLoading() {
        getWeatherBtn.textContent = "Loading...";
        getWeatherBtn.disabled = true;
        getWeatherBtn.classList.add("loading");
    }

    function hideLoading() {
        getWeatherBtn.textContent = "GET WEATHER INFO";
        getWeatherBtn.disabled = false;
        getWeatherBtn.classList.remove("loading");
    }
});