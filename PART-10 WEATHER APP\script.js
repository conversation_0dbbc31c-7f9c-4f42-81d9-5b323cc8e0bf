document.addEventListener("DOMContentLoaded", function () {
    const cityInput = document.getElementById("city-input");
    const getWeatherBtn = document.getElementById("get-weather-btn");
    const weatherInfo = document.getElementById("weather-info");
    const cityNameDisplay = document.getElementById("city-name");
    const temperatureDisplay = document.getElementById("temperature");
    const DescriptionDisplay = document.getElementById("description");
    const errorMessage = document.getElementById("error-message");

    getWeatherBtn.addEventListener("click", function () {
        let city = cityInput.ariaValueMax.trim();
        

    });