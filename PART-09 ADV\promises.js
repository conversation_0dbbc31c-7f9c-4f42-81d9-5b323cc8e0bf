function fetchData() {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            let success = true;
            if (success) {
                resolve("Data fetched successfully!");
            } else {
                reject("Error fetching data!");
            }
        }, 3000); 
     })
}

fetchData()
    .then((Data) => {
        console.log(Data);
        return `ANISH GUPTA`
    })
    .then((value) => {
        console.log(value);
    })
    .catch((Error) => console.error(Error));
        