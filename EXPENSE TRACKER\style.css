* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
    padding: 20px;
}

.container {
    max-width: 600px;
    margin: 0 auto;
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

h1 {
    text-align: center;
    color: #333;
    margin-bottom: 20px;
}

form {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
}

input {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

button {
    padding: 10px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

button:hover {
    background-color: #45a049;
}

#expense-list {
    list-style: none;
    margin-top: 20px;
}

#expense-list li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    margin-bottom: 5px;
    background-color: #f9f9f9;
    border-radius: 4px;
}

#expense-list button {
    background-color: #ff4444;
    padding: 5px 10px;
    font-size: 14px;
}

#expense-list button:hover {
    background-color: #cc0000;
}

#total {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 2px solid #eee;
}

h2, h3 {
    color: #333;
    margin-bottom: 10px;
}

#total-amount {
    font-weight: bold;
    color: #4CAF50;
}