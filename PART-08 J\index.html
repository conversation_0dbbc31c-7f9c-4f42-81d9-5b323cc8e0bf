<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS Classes Manipulation</title>
    <style>
        .highlight {
            background-color: yellow;
            color: black;
            padding: 5px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <section id="example-10">
        <h2>CSS classes manipulation</h2>
        <p id="descriptionText">This is some sample text that can be highlighted.</p>
        <button id="toggleHighlight">Toggle Highlight</button>
    </section>
    <script src="script.js"></script>
</body>
</html>
