// Task 1: Async-Await with Promise.all

// Function that returns a promise resolving with user data after 1 second
function fetchUser() {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({ id: 1, name: "<PERSON>", email: "<EMAIL>" });
        }, 1000);
    });
}

// Function that returns a promise resolving with posts data after 1 second
function fetchPosts() {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve([
                { id: 1, title: "First Post", content: "This is the first post" },
                { id: 2, title: "Second Post", content: "This is the second post" },
                { id: 3, title: "Third Post", content: "This is the third post" }
            ]);
        }, 1000);
    });
}

// Main function using async-await and Promise.all to fetch both simultaneously
async function fetchAllData() {
    try {
        console.log("Starting to fetch user and posts simultaneously...");
        
        // Use Promise.all to run both promises concurrently
        const [user, posts] = await Promise.all([
            fetchUser(),
            fetchPosts()
        ]);
        
        console.log("User data:", user);
        console.log("Posts data:", posts);
        
        return { user, posts };
    } catch (error) {
        console.error("Error fetching data:", error);
    }
}

// Execute the function
fetchAllData();

// Task 2: Error Handling in Async/Await with Promise.all

// Function that returns a promise that resolves successfully after 1 second
function fetchSuccess() {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({ status: "success", message: "Data fetched successfully!", data: { value: 42 } });
        }, 1000);
    });
}

// Function that returns a promise that rejects with an error after 1 second
function fetchFailure() {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            reject(new Error("Failed to fetch data from server!"));
        }, 1000);
    });
}

// Main function that handles both success and failure cases with Promise.all
async function handlePromises() {
    console.log("\n--- Task 2: Error Handling ---");
    console.log("Starting to fetch with Promise.all (expecting failure)...");
    
    try {
        // This will fail because fetchFailure() rejects
        const [successResult, failureResult] = await Promise.all([
            fetchSuccess(),
            fetchFailure()
        ]);
        
        console.log("Success result:", successResult);
        console.log("Failure result:", failureResult);
        
    } catch (error) {
        console.error("Promise.all failed:", error.message);
        
        // Alternative approach: Handle individual promises to see which succeeded/failed
        console.log("\nTrying individual promise handling...");
        
        try {
            const successResult = await fetchSuccess();
            console.log("Success result:", successResult);
        } catch (err) {
            console.error("fetchSuccess failed:", err.message);
        }
        
        try {
            const failureResult = await fetchFailure();
            console.log("Failure result:", failureResult);
        } catch (err) {
            console.error("fetchFailure failed:", err.message);
        }
    }
}

// Alternative function using Promise.allSettled for better error handling
async function handlePromisesWithAllSettled() {
    console.log("\n--- Using Promise.allSettled (better error handling) ---");
    
    try {
        const results = await Promise.allSettled([
            fetchSuccess(),
            fetchFailure()
        ]);
        
        results.forEach((result, index) => {
            if (result.status === 'fulfilled') {
                console.log(`Promise ${index + 1} succeeded:`, result.value);
            } else {
                console.error(`Promise ${index + 1} failed:`, result.reason.message);
            }
        });
        
    } catch (error) {
        console.error("Unexpected error:", error.message);
    }
}

// Execute Task 2 functions
setTimeout(() => {
    handlePromises().then(() => {
        // Run the allSettled version after the first one completes
        setTimeout(() => {
            handlePromisesWithAllSettled().then(() => {
                // Run Task 3 after Task 2 completes
                setTimeout(() => {
                    runTask3();
                }, 100);
            });
        }, 100);
    });
}, 2000); // Wait 2 seconds after Task 1 completes

// Task 3: Timeout with Async/Await and Promise.race

// Function that creates a timeout promise
function createTimeoutPromise(timeout) {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            reject(new Error("Timeout exceeded"));
        }, timeout);
    });
}

// Main function that uses Promise.race to implement timeout
async function fetchWithTimeout(promise, timeout) {
    try {
        // Race between the original promise and timeout promise
        const result = await Promise.race([
            promise,
            createTimeoutPromise(timeout)
        ]);
        
        return result;
    } catch (error) {
        if (error.message === "Timeout exceeded") {
            return "Timeout exceeded";
        }
        throw error; // Re-throw other errors
    }
}

// Test functions for demonstration
function fastPromise() {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve("Fast promise resolved in 500ms");
        }, 500);
    });
}

function slowPromise() {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve("Slow promise resolved in 2000ms");
        }, 2000);
    });
}

function failingPromise() {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            reject(new Error("Promise failed!"));
        }, 800);
    });
}

// Main function to run Task 3 demonstrations
async function runTask3() {
    console.log("\n--- Task 3: Timeout with Promise.race ---");
    
    // Test 1: Fast promise with generous timeout (should succeed)
    console.log("\nTest 1: Fast promise (500ms) with 1000ms timeout");
    try {
        const result1 = await fetchWithTimeout(fastPromise(), 1000);
        console.log("Result:", result1);
    } catch (error) {
        console.error("Error:", error.message);
    }
    
    // Test 2: Slow promise with short timeout (should timeout)
    console.log("\nTest 2: Slow promise (2000ms) with 1000ms timeout");
    try {
        const result2 = await fetchWithTimeout(slowPromise(), 1000);
        console.log("Result:", result2);
    } catch (error) {
        console.error("Error:", error.message);
    }
    
    // Test 3: Failing promise with generous timeout (should fail with original error)
    console.log("\nTest 3: Failing promise (800ms) with 1000ms timeout");
    try {
        const result3 = await fetchWithTimeout(failingPromise(), 1000);
        console.log("Result:", result3);
    } catch (error) {
        console.error("Error:", error.message);
    }
    
    // Test 4: Multiple promises with different timeouts
    console.log("\nTest 4: Multiple promises with different timeout settings");
    
    const promises = [
        { name: "Fast", promise: fastPromise(), timeout: 1000 },
        { name: "Slow", promise: slowPromise(), timeout: 1500 },
        { name: "Very Slow", promise: slowPromise(), timeout: 500 }
    ];
    
    for (const test of promises) {
        try {
            console.log(`\nTesting ${test.name} promise with ${test.timeout}ms timeout...`);
            const result = await fetchWithTimeout(test.promise, test.timeout);
            console.log(`${test.name} result:`, result);
        } catch (error) {
            console.error(`${test.name} error:`, error.message);
        }
    }
}
