document.addEventListener('DOMContentLoaded', () => {
    const expenseform = document.getElementById("expense-form");
    const expenseNameInput = document.getElementById("expense-name");
    const expenseAmountInput = document.getElementById("expense-amount");
    const expenseList = document.getElementById("expense-list");
    const totalAmountDisplay = document.getElementById("total-amount");

    let expense = JSON.parse(localStorage.getItem('expenses')) || [];
    let totalAmount = calculateTotal()

    renderExpense()


    function clearInputs() {
        document.getElementById('expense-name').value = '';
        document.getElementById('expense-amount').value = '';
    }

    expenseform.addEventListener('submit', function(e) {
        e.preventDefault();
        const name = expenseNameInput.value.trim();
        const amount = parseFloat(expenseAmountInput.value.trim());
        
        if (name !== "" && !isNaN(amount) && amount > 0) {
            const newExpense = {
                id: Date.now(),
                name,
                amount,
            };
            expense.push(newExpense);
            saveExpensesToLocal();
            renderExpense();
            updateTotal();
            clearInputs(); // Clear the inputs after successfully adding expense
        }
    });


    function renderExpense() {
        expenseList.innerHTML = "";
        expense.forEach(expense => {
            const li = document.createElement("li")
            li.innerHTML = `
            ${expense.name} - $${expense.amount}
            <button data-id ="${expense.id}"> delete </button>
            `
            expenseList.appendChild(li)
        })
    }





    function calculateTotal() {
        return expense.reduce((sum ,expense) => sum+expense.amount, 0)
        

}

    function saveExpensesToLocal() {
        localStorage.setItem('expenses', JSON.stringify(expense));
    }
    
    function updateTotal() {
        totalAmount = calculateTotal()
        totalAmountDisplay.textContent = totalAmount.toFixed(2);
    }

    // Add event listener for delete buttons
    expenseList.addEventListener('click', (e) => {
        if (e.target.tagName === 'BUTTON') {
            const expenseId = parseInt(e.target.getAttribute('data-id'));
            // Filter out the expense with the matching id
            expense = expense.filter(expense => expense.id !== expenseId);
            
            // Update localStorage and UI
            saveExpensesToLocal();
            renderExpense();
            updateTotal();
        }
    });
});
   