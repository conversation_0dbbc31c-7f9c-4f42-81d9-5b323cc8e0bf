function fetchUserdata(){
    return new Promise((resolve, reject) =>{
        setTimeout(() => {
            resolve({name: "An<PERSON>", url:"https://chaicode.com"})
        },3000)
    })
}

async function getUserData() {
    try {
        console.log(`Fetching User Data...`);
        const userData = await fetchUserdata()
        console.log("User data fetched successfully");
        console.log("User data:",userData)
    } catch (error) {
        console.log("Error fetching data",error)
    }
}
getUserData();