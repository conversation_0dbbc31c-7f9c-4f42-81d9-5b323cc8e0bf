/*
Write a function named `create<PERSON>ea<PERSON><PERSON>` that returns another function . The returned function should take one parameter, 'teaType`, and return a message like '"Making green tea"'.
Store the returned function in a variable named `teaMaker` and call it `"Green tea"`.
*/

function createTeaMaker(name) {
    let score = 100;
    let length = 10
    return function (teaType) {
        return `Making ${teaType} ${name} ${score} ${length}`;
    };
}

let teaMaker = createTeaMaker("Anish");
let result = teaMaker("Green tea");
console.log(result);