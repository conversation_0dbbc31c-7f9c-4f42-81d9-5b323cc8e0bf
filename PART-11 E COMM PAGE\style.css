/* Simple E-Commerce Page CSS */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
    padding: 20px;
    line-height: 1.6;
}

/* Container */
.container {
    max-width: 800px;
    margin: 0 auto;
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

/* Headings */
h1 {
    text-align: center;
    color: #333;
    margin-bottom: 30px;
    font-size: 2.5rem;
    border-bottom: 3px solid #007bff;
    padding-bottom: 10px;
}

h2 {
    color: #333;
    margin: 30px 0 15px 0;
    font-size: 1.8rem;
    text-transform: capitalize;
}

/* Product List */
#product-list {
    min-height: 200px;
    padding: 20px;
    border: 2px dashed #ddd;
    border-radius: 8px;
    background-color: #fafafa;
    margin-bottom: 20px;
    text-align: center;
    color: #666;
}

/* Product Cards (for when products are added) */
.product {
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: box-shadow 0.3s ease;
}

.product:hover {
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.product-info h3 {
    color: #333;
    margin-bottom: 5px;
}

.product-price {
    color: #007bff;
    font-weight: bold;
    font-size: 1.2rem;
}

.add-to-cart {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.add-to-cart:hover {
    background-color: #218838;
}

/* Cart Items */
#card-items {
    min-height: 100px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #f9f9f9;
    margin-bottom: 20px;
}

/* Empty Cart Message */
#empty-cart {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 20px;
}

/* Cart Item */
.cart-item {
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cart-item-info {
    flex-grow: 1;
}

.cart-item-name {
    font-weight: bold;
    color: #333;
}

.cart-item-price {
    color: #007bff;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 15px;
}

.quantity-btn {
    background-color: #007bff;
    color: white;
    border: none;
    width: 25px;
    height: 25px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 14px;
}

.quantity-btn:hover {
    background-color: #0056b3;
}

.quantity {
    font-weight: bold;
    min-width: 20px;
    text-align: center;
}

.remove-item {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 3px;
    cursor: pointer;
}

.remove-item:hover {
    background-color: #c82333;
}

/* Cart Total */
#card-total {
    background-color: #e9ecef;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    border: 2px solid #007bff;
}

#card-total p {
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
}

#total-price {
    color: #28a745;
    font-size: 2rem;
}

/* Checkout Button */
#checkout-btn {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 1.2rem;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    width: 100%;
    max-width: 200px;
}

#checkout-btn:hover {
    background-color: #0056b3;
}

#checkout-btn:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
}

/* Hidden Class */
.hidden {
    display: none !important;
}

/* Responsive Design */
@media (max-width: 600px) {
    .container {
        padding: 20px;
        margin: 10px;
    }
    
    h1 {
        font-size: 2rem;
    }
    
    .product {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .cart-item {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .quantity-controls {
        margin: 10px 0;
    }
}
