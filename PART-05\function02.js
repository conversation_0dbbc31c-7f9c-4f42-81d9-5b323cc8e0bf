/*
Create a function named `orderTea` that take one parameter,`teatype`. Inside this function, create another function named 'confirmOrder` that returns a message like `"order confirmed for chai".
Call `confirmOrder` from within `orderTea` and return the result.
*/

function orderTea(teatype) {
    function confirmOrder() {
        return `order confirmed for ${teatype}`;
    }
    return confirmOrder();
}

let orderConfirmation = orderTea("chai");
console.log(orderConfirmation);