/* Weather App CSS Styles */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    color: #333;
}

/* Main Container */
.container {
    background: rgba(255, 255, 255, 0.95);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 500px;
    width: 100%;
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.container:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

/* Main Title */
.container h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 30px;
    color: #2d3436;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    letter-spacing: 1px;
}

/* Input Container */
.input-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 30px;
}

/* City Input Field */
#city-input {
    padding: 15px 20px;
    font-size: 1.1rem;
    border: 2px solid #ddd;
    border-radius: 50px;
    outline: none;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    text-align: center;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

#city-input:focus {
    border-color: #74b9ff;
    box-shadow: 0 0 0 3px rgba(116, 185, 255, 0.2);
    transform: scale(1.02);
}

#city-input::placeholder {
    color: #999;
    font-style: italic;
}

/* Get Weather Button */
#get-weather-btn {
    padding: 15px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    color: white;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 5px 15px rgba(116, 185, 255, 0.4);
}

#get-weather-btn:hover {
    background: linear-gradient(135deg, #0984e3 0%, #2d3436 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(116, 185, 255, 0.6);
}

#get-weather-btn:active {
    transform: translateY(0);
    box-shadow: 0 3px 10px rgba(116, 185, 255, 0.4);
}

/* Weather Info Container */
#weather-info {
    background: linear-gradient(135deg, #00cec9 0%, #55a3ff 100%);
    padding: 25px;
    border-radius: 15px;
    margin: 20px 0;
    box-shadow: 0 10px 30px rgba(0, 206, 201, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: fadeInUp 0.6s ease-out;
}

#weather-info h2 {
    margin: 10px 0;
    color: white;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

/* City Name Styling */
#city-name {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 15px !important;
    text-transform: uppercase;
    letter-spacing: 2px;
}

/* Temperature Styling */
#temperature {
    font-size: 3rem;
    font-weight: 900;
    margin: 15px 0 !important;
    text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.4);
}

/* Description Styling */
#description {
    font-size: 1.3rem;
    font-weight: 400;
    font-style: italic;
    text-transform: capitalize;
    opacity: 0.9;
}

/* Error Message */
#error-message {
    background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
    color: white;
    padding: 15px 20px;
    border-radius: 10px;
    margin: 20px 0;
    font-weight: 500;
    box-shadow: 0 5px 15px rgba(232, 67, 147, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: shake 0.6s ease-in-out;
}

/* Hidden Class */
.hidden {
    display: none !important;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

/* Loading Animation for Button */
#get-weather-btn.loading {
    position: relative;
    color: transparent;
}

#get-weather-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        padding: 30px 20px;
    }
    
    .container h1 {
        font-size: 2rem;
    }
    
    #city-input {
        font-size: 1rem;
        padding: 12px 18px;
    }
    
    #get-weather-btn {
        font-size: 1rem;
        padding: 12px 25px;
    }
    
    #city-name {
        font-size: 1.5rem;
    }
    
    #temperature {
        font-size: 2.5rem;
    }
    
    #description {
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    body {
        padding: 10px;
    }
    
    .container {
        padding: 25px 15px;
    }
    
    .container h1 {
        font-size: 1.8rem;
        margin-bottom: 25px;
    }
    
    .input-container {
        gap: 12px;
        margin-bottom: 25px;
    }
    
    #weather-info {
        padding: 20px;
    }
    
    #city-name {
        font-size: 1.3rem;
    }
    
    #temperature {
        font-size: 2.2rem;
    }
}
