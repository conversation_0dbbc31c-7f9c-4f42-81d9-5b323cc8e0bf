function car(make, model) {
   this.make= make;
   this.model = model;
}

let myCar = new car("toyota", "camry");
// console.log(myCar);

let myanotherCar = new car("honda", "civic");
// console.log(myanotherCar);

function tea(type) {
    this.type = type;
    this.description = function () {
        return `This is a cup of ${this.type} tea`;
    };
}
let lemonTea = new tea("lemon");
// console.log(lemonTea.description());

function animal(species) {
    this.species = species;
}

animal.prototype.sound(){
    return `${this.species} makes a sound`;
}
let dog = new animal("dog");
console.log(dog.sound());