let car = {
    name: "BMW",
    model: "X5",
    color: "red",
    start: function () {
        return `${this.name} is of ${this.color} color`;
    },
};
// console.log(car.start());


//class example 
class vehicle {
    constructor(name, color) {
        this.name = name;
        this.color = color;
    }

    start() {
        return `${this.name} is of ${this.color} color`;
    }
}

class car extends vehicle {
    drive() {
        return `${this.name} :this is an example of inheritance`;
    }
}

let myCar = new car("toyota", "white");
console.log(myCar.start());
console.log(myCar.drive());