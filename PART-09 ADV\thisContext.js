const person = {
    name: "<PERSON><PERSON>",
    age: 20,
    greet: function () {
        console.log(`Hello, my name is ${this.name} and my age is ${this.age}`);
    },
}

person.greet();
const greetFunction = person.greet;
greetFunction();

const boundgreet = person.greet.bind({ name:"john", age: 30 });
boundgreet();

//bind,call and apply

// CALL - executes immediately with individual arguments
console.log("\n--- Using CALL ---");
person.greet.call({ name: "<PERSON>", age: 25 });

// APPLY - executes immediately with arguments as array
console.log("\n--- Using APPLY ---");
person.greet.apply({ name: "<PERSON>", age: 35 });

// BIND - creates new function for later use
console.log("\n--- Using BIND ---");
const boundToTom = person.greet.bind({ name: "<PERSON>", age: 28 });
boundToTom();

// Example with parameters
const personWithParams = {
    introduce: function(greeting, hobby) {
        console.log(`${greeting}! I'm ${this.name}, age ${this.age}. I love ${hobby}.`);
    }
};

console.log("\n--- CALL with parameters ---");
personWithParams.introduce.call({ name: "Emma", age: 22 }, "Hi", "coding");

console.log("\n--- APPLY with parameters ---");
personWithParams.introduce.apply({ name: "Alex", age: 30 }, ["Hello", "music"]);

console.log("\n--- BIND with parameters ---");
const boundIntroduce = personWithParams.introduce.bind({ name: "Lisa", age: 27 }, "Hey");
boundIntroduce("reading");