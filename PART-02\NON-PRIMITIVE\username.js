

let username = {
    "first name": "<PERSON><PERSON>",
    "last name": "<PERSON>",
    isloggedin: true,
}

username["first name"] = "<PERSON><PERSON><PERSON><PERSON>";
username["last name"] = "<PERSON><PERSON><PERSON>";

console.log(username["first name"]); // Output: Anish
console.log(username["last name"]); // Output: Gupta
console.log(username); // Output: { "first name": "<PERSON><PERSON><PERSON><PERSON>", "last name": "<PERSON><PERSON><PERSON>" }
console.log(typeof username); // Output: object

// console.log(username); // Output: { "first name": "<PERSON><PERSON>", "last name": "<PERSON>" }
// console.log(typeof username); // Output: object