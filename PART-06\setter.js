class employee {
    constructor(name, salary) {
        this.name = name;
        this.salary = salary;
    }

    get salary() {
        return `you are not allowed to see the salary`;
    }

    set salary(value) {
        if (value <0) {
            throw new Error("salary can't be negative");
        }
        else {
            this._salary = value;
        }
        
    }
   
}
let emp = new emp("anish", 1000000);
console.log(emp.salary);