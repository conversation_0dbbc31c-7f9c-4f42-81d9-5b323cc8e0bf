document.addEventListener("DOMContentLoaded", () => {
    const products = [
        { id: 1, name: "Product 1", price: 10 },
        { id: 2, name: "Product 2", price: 20 },
        { id: 3, name: "Product 3", price: 30 }
    ];

    const cart = [];

    // Get DOM elements (using correct IDs from your HTML)
    const productList = document.getElementById("product-list");
    const cartItems = document.getElementById("card-items"); // Note: your HTML uses "card-items"
    const emptyCartMessage = document.getElementById("empty-cart");
    const cartTotal = document.getElementById("card-total"); // Note: your HTML uses "card-total"
    const totalPriceDisplay = document.getElementById("total-price");
    const checkoutButton = document.getElementById("checkout-btn");
    
    // Display products
    products.forEach(product => {
        const productDiv = document.createElement("div"); // Fixed: added space between const and productDiv
        productDiv.classList.add("product");
        productDiv.innerHTML = `
            <div class="product-info">
                <h3>${product.name}</h3>
                <span class="product-price">$${product.price.toFixed(2)}</span>
            </div>
            <button class="add-to-cart" data-id="${product.id}">Add to Cart</button>
        `;
        productList.appendChild(productDiv);
    });

    // Add to cart function
    function addToCart(product) {
        const existingItem = cart.find(item => item.id === product.id);
        
        if (existingItem) {
            existingItem.quantity += 1;
        } else {
            cart.push({ ...product, quantity: 1 });
        }
        
        updateCartDisplay();
        console.log("Product added to cart:", product);
    }

    // Update cart display function
    function updateCartDisplay() {
        // Clear current cart items except empty message
        const cartChildren = Array.from(cartItems.children);
        cartChildren.forEach(child => {
            if (child.id !== 'empty-cart') {
                child.remove();
            }
        });
        
        if (cart.length === 0) {
            emptyCartMessage.style.display = 'block';
            cartTotal.classList.add("hidden");
        } else {
            emptyCartMessage.style.display = 'none';
            
            cart.forEach(item => {
                const cartItemDiv = document.createElement("div");
                cartItemDiv.classList.add("cart-item");
                cartItemDiv.innerHTML = `
                    <div class="cart-item-info">
                        <div class="cart-item-name">${item.name}</div>
                        <div class="cart-item-price">$${item.price.toFixed(2)}</div>
                    </div>
                    <div class="quantity-controls">
                        <button class="quantity-btn" onclick="updateQuantity(${item.id}, 'decrease')">-</button>
                        <span class="quantity">${item.quantity}</span>
                        <button class="quantity-btn" onclick="updateQuantity(${item.id}, 'increase')">+</button>
                    </div>
                    <button class="remove-item" onclick="removeFromCart(${item.id})">Remove</button>
                `;
                cartItems.appendChild(cartItemDiv);
            });
            
            updateTotal();
            cartTotal.classList.remove("hidden");
        }
    }

    // Update total price function
    function updateTotal() {
        const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        totalPriceDisplay.textContent = `$${total.toFixed(2)}`;
    }

    // Update quantity function
    window.updateQuantity = function(productId, action) {
        const item = cart.find(item => item.id === productId);
        
        if (item) {
            if (action === "increase") {
                item.quantity += 1;
            } else if (action === "decrease") {
                item.quantity -= 1;
                if (item.quantity <= 0) {
                    removeFromCart(productId);
                    return;
                }
            }
            updateCartDisplay();
        }
    }

    // Remove from cart function
    window.removeFromCart = function(productId) {
        const index = cart.findIndex(item => item.id === productId);
        if (index > -1) {
            cart.splice(index, 1);
            updateCartDisplay();
        }
    }

    // Checkout button event listener
    checkoutButton.addEventListener("click", () => {
        if (cart.length === 0) {
            alert("Your cart is empty!");
            return;
        }
        
        const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        alert(`Checkout complete! Total: $${total.toFixed(2)}`);
        
        // Clear cart after checkout
        cart.length = 0;
        updateCartDisplay();
    });

    productList.addEventListener("click", event => {
        if (event.target.tagName === "BUTTON") {
            const productID = parseInt(event.target.getAttribute("data-id"));
            const product = products.find(p => p.id === productID);
            addToCart(product);
        }
    });

}); // This closes the DOMContentLoaded function
