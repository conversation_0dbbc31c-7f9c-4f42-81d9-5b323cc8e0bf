<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Expense tracker</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>Expense tracker</h1>
        <form id="expense-form">
            <input
            type="text"
            id="expense-name"
            placeholder="Enter expense name"
            required
            />
            <input
            type ="number"
            id="expense-amount"
            placeholder="Enter expense amount"
            required
            />
            <button type="submit">Add expense</button>
        </form>



        <h2>Expenses</h2>
        <ul id="expense-list">
        <!-- expense will dhynamically added here -->    
        </ul>

            <div id="total">
                <h3> Total: <span id="total-amount">0.00</span></h3>
            </div>
        </div>

        <script src="script.js"></script>
</body>
</html>